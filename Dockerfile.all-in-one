# =============================================================================
# 磐石钱包管理系统 v5.1.7 - All-in-One Docker镜像
# =============================================================================
# 版本: 5.1.7
# 新增功能:
# - AI信号智能识别与处理
# - 高级跟卖逻辑优化
# - 无锁架构性能提升
# - Redis连接池优化
# - 假交易日志清理
#
# 包含组件:
# - 交易后端 (newcopy_v2) - 基于Solana 1.16
# - 监控后端 (copy-bot) - 基于Solana 2.1
# - Redis数据库
# - 前端界面 (Nginx)
# =============================================================================

# 多阶段构建：编译阶段
FROM rust:1.75 AS rust-builder

# 安装编译依赖
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    ca-certificates \
    git

# 设置Rust工具链
RUN rustup default 1.75
RUN rustup target add x86_64-unknown-linux-gnu

# 编译交易后端 (使用Solana 1.16) - v5.1.7 新增AI信号处理
WORKDIR /build/trading-backend
COPY qingqikaifaxxxxxxxxxxxxxxxxx/copybot-fix-price-debug-log/newcopy_v2 .
RUN cargo build --release

# 编译监控后端 (使用Solana 2.1) - v5.1.7 优化跟卖逻辑
WORKDIR /build/monitor-backend
COPY taoli .
RUN cargo build --release

# 前端构建阶段 - v5.1.7 磐石钱包管理界面
FROM node:20-alpine AS frontend-builder

WORKDIR /build
# 构建钱包管理前端 - 支持AI信号监控和跟卖配置
COPY 2.0/wallet-admin-frontend .
RUN npm install && npm run build

# =============================================================================
# 最终运行阶段 - 磐石钱包管理系统 v5.1.7
# =============================================================================
FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV RUST_LOG=info
ENV PANSHI_VERSION=5.1.7
ENV PANSHI_BUILD_DATE=$(date -u +"%Y-%m-%d %H:%M:%S UTC")

# 添加版本标签
LABEL version="5.1.7"
LABEL description="磐石钱包管理系统 - AI信号+跟卖逻辑版本"
LABEL maintainer="磐石社区"
LABEL features="AI信号处理,高级跟卖逻辑,无锁架构,Redis连接池优化"

# 安装运行时依赖 - v5.1.7 优化版本
RUN apt-get update && apt-get install -y \
    redis-server \
    nginx \
    supervisor \
    curl \
    ca-certificates \
    libssl-dev \
    htop \
    && rm -rf /var/lib/apt/lists/*

# 创建应用目录结构 - v5.1.7
RUN mkdir -p /app/trading /app/monitor /app/frontend /app/logs /app/config

# 从构建阶段复制编译好的程序 - v5.1.7
COPY --from=rust-builder /build/trading-backend/target/release/newcopy_v2 /app/trading/
COPY --from=rust-builder /build/trading-backend/settings.toml /app/trading/

# 修复单体容器中的Redis连接配置 - v5.1.7 优化连接池
RUN sed -i 's|redis://redis:6379|redis://127.0.0.1:6379|g' /app/trading/settings.toml

COPY --from=rust-builder /build/monitor-backend/target/release/copy-bot /app/monitor/
COPY --from=rust-builder /build/monitor-backend/config.toml /app/monitor/
COPY --from=rust-builder /build/monitor-backend/idls /app/monitor/idls/

# 从前端构建阶段复制构建好的文件 - v5.1.7 磐石钱包界面
COPY --from=frontend-builder /build/dist /app/frontend/

# 创建版本信息文件
RUN echo "磐石钱包管理系统 v5.1.7" > /app/VERSION
RUN echo "构建时间: $(date -u)" >> /app/VERSION
RUN echo "新增功能: AI信号处理, 高级跟卖逻辑, 无锁架构优化" >> /app/VERSION

# 配置 Nginx - v5.1.7 磐石钱包前端代理
RUN rm -f /etc/nginx/sites-enabled/default
COPY <<EOF /etc/nginx/sites-available/default
# =============================================================================
# 磐石钱包管理系统 v5.1.7 - Nginx配置
# 功能: 前端界面 + API代理 + AI信号监控
# =============================================================================
server {
    listen 80;
    server_name localhost;
    root /app/frontend;
    index index.html;

    # 添加版本响应头
    add_header X-Panshi-Version "5.1.7";
    add_header X-Panshi-Features "AI信号,跟卖逻辑,无锁架构";

    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 处理前端路由
    location / {
        try_files \$uri \$uri/ /index.html;

        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)\$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API代理到交易后端 - v5.1.7 AI信号处理API
    location /api/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # 支持SSE流 - AI信号实时推送
        proxy_buffering off;
        proxy_cache off;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }
}
EOF

# 启用站点
RUN ln -s /etc/nginx/sites-available/default /etc/nginx/sites-enabled/

# 配置 Redis - v5.1.7 优化连接池配置
RUN sed -i 's/^bind 127.0.0.1 ::1/bind 127.0.0.1/' /etc/redis/redis.conf
RUN sed -i 's/^# maxclients 10000/maxclients 10000/' /etc/redis/redis.conf
RUN sed -i 's/^# timeout 0/timeout 300/' /etc/redis/redis.conf

# 设置执行权限 - v5.1.7
RUN chmod +x /app/trading/newcopy_v2
RUN chmod +x /app/monitor/copy-bot

# 创建 Supervisor 配置 - v5.1.7 四服务管理
COPY <<EOF /etc/supervisor/conf.d/supervisord.conf
# =============================================================================
# 磐石钱包管理系统 v5.1.7 - Supervisor配置
# 管理四个核心服务: Redis + Nginx + 交易后端 + 监控后端
# =============================================================================
[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisord.log
pidfile=/var/run/supervisord.pid

[program:redis]
command=redis-server /etc/redis/redis.conf
autostart=true
autorestart=true
stdout_logfile=/var/log/redis.log
stderr_logfile=/var/log/redis.log
priority=100

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
stdout_logfile=/var/log/nginx.log
stderr_logfile=/var/log/nginx.log
priority=300

[program:trading-backend]
command=/app/trading/newcopy_v2
directory=/app/trading
autostart=true
autorestart=true
stdout_logfile=/var/log/trading-backend.log
stderr_logfile=/var/log/trading-backend.log
environment=RUST_LOG=info,REDIS_URL="redis://127.0.0.1:6379",PANSHI_VERSION="5.1.7"
priority=200

[program:monitor-backend]
command=/app/monitor/copy-bot
directory=/app/monitor
autostart=true
autorestart=true
stdout_logfile=/var/log/monitor-backend.log
stderr_logfile=/var/log/monitor-backend.log
environment=RUST_LOG=info,REDIS_URL="redis://127.0.0.1:6379",PANSHI_VERSION="5.1.7"
priority=200
EOF

# 创建日志目录和文件 - v5.1.7
RUN mkdir -p /var/log && \
    touch /var/log/redis.log /var/log/nginx.log /var/log/trading-backend.log /var/log/monitor-backend.log /var/log/supervisord.log

# 创建健康检查脚本
RUN echo '#!/bin/bash' > /app/healthcheck.sh && \
    echo 'curl -f http://localhost/api/v1/health >/dev/null 2>&1 && redis-cli ping >/dev/null 2>&1' >> /app/healthcheck.sh && \
    chmod +x /app/healthcheck.sh

# 添加健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /app/healthcheck.sh

# 只暴露前端端口，后端通过Nginx代理访问
EXPOSE 80

# 添加启动信息
RUN echo '#!/bin/bash' > /app/startup.sh && \
    echo 'echo "==============================================================================="' >> /app/startup.sh && \
    echo 'echo "🚀 磐石钱包管理系统 v5.1.7 启动中..."' >> /app/startup.sh && \
    echo 'echo "📦 包含服务: Redis + Nginx + 交易后端 + 监控后端"' >> /app/startup.sh && \
    echo 'echo "🆕 新增功能: AI信号处理 + 高级跟卖逻辑 + 无锁架构优化"' >> /app/startup.sh && \
    echo 'echo "🌐 前端访问: http://localhost"' >> /app/startup.sh && \
    echo 'echo "📊 API接口: http://localhost/api/"' >> /app/startup.sh && \
    echo 'echo "==============================================================================="' >> /app/startup.sh && \
    echo '/usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf' >> /app/startup.sh && \
    chmod +x /app/startup.sh

# 启动所有服务 - v5.1.7
CMD ["/app/startup.sh"]