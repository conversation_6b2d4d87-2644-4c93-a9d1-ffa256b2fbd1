# 多阶段构建：编译阶段
FROM rust:1.75 AS rust-builder

# 安装编译依赖
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    ca-certificates \
    git

# 设置Rust工具链
RUN rustup default 1.75
RUN rustup target add x86_64-unknown-linux-gnu

# 编译交易后端 (使用Solana 1.16)
WORKDIR /build/trading-backend
COPY qingqikaifaxxxxxxxxxxxxxxxxx/copybot-fix-price-debug-log/newcopy_v2 .
RUN cargo build --release

# 编译监控后端 (使用Solana 2.1)
WORKDIR /build/monitor-backend
COPY taoli .
RUN cargo build --release

# 前端构建阶段
FROM node:20-alpine AS frontend-builder

WORKDIR /build
# 构建钱包管理前端
COPY 2.0/wallet-admin-frontend .
RUN npm install && npm run build

# 最终运行阶段
FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV RUST_LOG=info

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    redis-server \
    nginx \
    supervisor \
    curl \
    ca-certificates \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# 创建应用目录
RUN mkdir -p /app/trading /app/monitor /app/frontend /app/logs

# 从构建阶段复制编译好的程序
COPY --from=rust-builder /build/trading-backend/target/release/newcopy_v2 /app/trading/
COPY --from=rust-builder /build/trading-backend/settings.toml /app/trading/

# 修复单体容器中的Redis连接配置
RUN sed -i 's|redis://redis:6379|redis://127.0.0.1:6379|g' /app/trading/settings.toml

COPY --from=rust-builder /build/monitor-backend/target/release/copy-bot /app/monitor/
COPY --from=rust-builder /build/monitor-backend/config.toml /app/monitor/
COPY --from=rust-builder /build/monitor-backend/idls /app/monitor/idls/

# 从前端构建阶段复制构建好的文件
COPY --from=frontend-builder /build/dist /app/frontend/

# 配置 Nginx - 后端不对外暴露，只通过nginx代理访问
RUN rm -f /etc/nginx/sites-enabled/default
COPY <<EOF /etc/nginx/sites-available/default
server {
    listen 80;
    server_name localhost;
    root /app/frontend;
    index index.html;

    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 处理前端路由
    location / {
        try_files \$uri \$uri/ /index.html;

        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)\$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API代理到后端 - 后端只监听内部127.0.0.1，不对外暴露
    location /api/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # 支持SSE
        proxy_buffering off;
        proxy_cache off;
    }
}
EOF

# 启用站点
RUN ln -s /etc/nginx/sites-available/default /etc/nginx/sites-enabled/

# 配置 Redis
RUN sed -i 's/^bind 127.0.0.1 ::1/bind 127.0.0.1/' /etc/redis/redis.conf

# 设置执行权限
RUN chmod +x /app/trading/newcopy_v2
RUN chmod +x /app/monitor/copy-bot

# 创建 Supervisor 配置
COPY <<EOF /etc/supervisor/conf.d/supervisord.conf
[supervisord]
nodaemon=true
user=root

[program:redis]
command=redis-server /etc/redis/redis.conf
autostart=true
autorestart=true
stdout_logfile=/var/log/redis.log
stderr_logfile=/var/log/redis.log

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
stdout_logfile=/var/log/nginx.log
stderr_logfile=/var/log/nginx.log

[program:main-backend]
command=/app/backend/newcopy_v2
directory=/app/backend
autostart=true
autorestart=true
stdout_logfile=/var/log/main-backend.log
stderr_logfile=/var/log/main-backend.log
environment=RUST_LOG=info,REDIS_URL="redis://127.0.0.1:6379"

[program:monitor-backend]
command=/app/monitor/copy-bot
directory=/app/monitor
autostart=true
autorestart=true
stdout_logfile=/var/log/monitor-backend.log
stderr_logfile=/var/log/monitor-backend.log
environment=RUST_LOG=info,REDIS_URL="redis://127.0.0.1:6379"
EOF

# 创建日志目录和文件
RUN mkdir -p /var/log && \
    touch /var/log/redis.log /var/log/nginx.log /var/log/main-backend.log /var/log/monitor-backend.log

# 只暴露前端端口，后端不对外暴露
EXPOSE 80

# 启动所有服务
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"] 