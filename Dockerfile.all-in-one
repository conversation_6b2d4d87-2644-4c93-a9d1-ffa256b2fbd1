# All-in-One Docker构建文件 v5.1.6 - 0.4商用运行版
# 针对Rust编译慢问题的优化方案

# 多阶段构建：编译阶段
FROM rust:latest AS rust-builder

# 设置环境变量优化编译
ENV CARGO_NET_GIT_FETCH_WITH_CLI=true
ENV CARGO_REGISTRIES_CRATES_IO_PROTOCOL=sparse

# 安装编译依赖
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# ===== 主后端缓存优化构建 =====
WORKDIR /build/main-backend

# 1. 先只复制依赖文件，利用Docker缓存
COPY qingqikaifaxxxxxxxxxxxxxxxxx/copybot-fix-price-debug-log/newcopy_v2/Cargo.toml ./
COPY qingqikaifaxxxxxxxxxxxxxxxxx/copybot-fix-price-debug-log/newcopy_v2/Cargo.lock ./

# 2. 创建虚拟main.rs编译依赖（缓存关键步骤）
RUN mkdir src && echo 'fn main() {}' > src/main.rs && cargo build --release && rm -rf src

# 3. 再复制真正源码，只重编译变更部分
COPY qingqikaifaxxxxxxxxxxxxxxxxx/copybot-fix-price-debug-log/newcopy_v2/src ./src
COPY qingqikaifaxxxxxxxxxxxxxxxxx/copybot-fix-price-debug-log/newcopy_v2/settings.toml ./
RUN touch src/main.rs && cargo build --release

# ===== 监控后端缓存优化构建 =====
WORKDIR /build/monitor-backend

# 1. 先复制依赖文件
COPY taoli/Cargo.toml ./
COPY taoli/Cargo.lock ./

# 2. 虚拟编译依赖（支持lib+bin混合项目）
RUN mkdir src && \
    echo 'fn main() {}' > src/main.rs && \
    echo 'pub fn lib_placeholder() {}' > src/lib.rs && \
    cargo build --release && rm -rf src

# 3. 复制真正源码重编译
COPY taoli/src ./src
COPY taoli/config.toml ./
RUN touch src/main.rs src/lib.rs && cargo build --release

# ===== 前端缓存优化构建 =====
FROM node:20-alpine AS frontend-builder

WORKDIR /build

# Alpine需要额外工具支持musl
RUN apk add --no-cache python3 make g++

# 1. 先复制package文件，利用npm缓存
COPY 2.0/wallet-admin-frontend/package*.json ./
# 使用npm install而不是ci来避免musl依赖问题
RUN npm install

# 2. 再复制源码构建
COPY 2.0/wallet-admin-frontend .
RUN npm run build

# ===== 最终运行阶段 =====
FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV RUST_LOG=info
ENV TZ=Asia/Shanghai

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    redis-server \
    nginx \
    supervisor \
    curl \
    ca-certificates \
    libssl-dev \
    tzdata \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 创建应用目录
RUN mkdir -p /app/backend /app/monitor /app/frontend /app/logs /var/log/supervisor

# 从构建阶段复制编译好的程序
COPY --from=rust-builder /build/main-backend/target/release/newcopy_v2 /app/backend/
COPY --from=rust-builder /build/main-backend/settings.toml /app/backend/

# 修复Redis连接配置
RUN sed -i 's|redis://redis:6379|redis://127.0.0.1:6379|g' /app/backend/settings.toml

# 复制监控后端
COPY --from=rust-builder /build/monitor-backend/target/release/pump-demo /app/monitor/
COPY --from=rust-builder /build/monitor-backend/config.toml /app/monitor/

# 复制前端构建文件
COPY --from=frontend-builder /build/dist /app/frontend/

# 配置Nginx
RUN rm -f /etc/nginx/sites-enabled/default
COPY <<EOF /etc/nginx/sites-available/default
server {
    listen 80;
    server_name localhost;
    root /app/frontend;
    index index.html;

    # 性能优化
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 前端路由
    location / {
        try_files \$uri \$uri/ /index.html;
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_buffering off;
        proxy_cache off;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "HebiNg v5.1.6 Running";
        add_header Content-Type text/plain;
    }
}
EOF

RUN ln -s /etc/nginx/sites-available/default /etc/nginx/sites-enabled/

# 配置Redis
RUN sed -i 's/^bind 127.0.0.1 ::1/bind 127.0.0.1/' /etc/redis/redis.conf && \
    echo "daemonize no" >> /etc/redis/redis.conf && \
    echo "supervised systemd" >> /etc/redis/redis.conf

# 设置执行权限
RUN chmod +x /app/backend/newcopy_v2 /app/monitor/pump-demo

# Supervisor配置
COPY <<EOF /etc/supervisor/conf.d/supervisord.conf
[supervisord]
nodaemon=true
user=root

[program:redis]
command=redis-server /etc/redis/redis.conf
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/redis.log
stderr_logfile=/var/log/supervisor/redis.log

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/nginx.log
stderr_logfile=/var/log/supervisor/nginx.log

[program:main-backend]
command=/app/backend/newcopy_v2
directory=/app/backend
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/main-backend.log
stderr_logfile=/var/log/supervisor/main-backend.log
environment=RUST_LOG=info,REDIS_URL="redis://127.0.0.1:6379"

[program:monitor-backend]
command=/app/monitor/pump-demo
directory=/app/monitor
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/monitor-backend.log
stderr_logfile=/var/log/supervisor/monitor-backend.log
environment=RUST_LOG=info,REDIS_URL="redis://127.0.0.1:6379"
EOF

# 创建日志文件
RUN touch /var/log/supervisor/redis.log \
          /var/log/supervisor/nginx.log \
          /var/log/supervisor/main-backend.log \
          /var/log/supervisor/monitor-backend.log

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

# 暴露端口
EXPOSE 80

# 元数据
LABEL version="5.1.6"
LABEL description="HebiNg All-in-One Wallet Admin System v5.1.6 - 0.4商用运行版"

# 启动
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]