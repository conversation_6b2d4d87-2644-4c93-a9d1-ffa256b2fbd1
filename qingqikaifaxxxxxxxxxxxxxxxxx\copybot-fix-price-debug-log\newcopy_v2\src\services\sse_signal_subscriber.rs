use anyhow::{Result, Context};
use eventsource_stream::Eventsource;
use futures_util::StreamExt;
use redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use dashmap::DashMap;
use tracing::{info, error, warn, debug};
use uuid::Uuid;
use chrono;
use tokio::sync::Mutex;

// 全局存储待处理的SSE信号 - 无锁版本
lazy_static::lazy_static! {
    pub static ref PENDING_SSE_SIGNALS: Arc<DashMap<String, SseSignalData>> = Arc::new(DashMap::new());

    // 全局Redis连接池 - 避免重复建立连接
    pub static ref REDIS_CONNECTION_POOL: Arc<Mutex<Option<redis::aio::MultiplexedConnection>>> = Arc::new(Mutex::new(None));
}

/// SSE信号数据格式
#[derive(Debug, <PERSON><PERSON>, Deserialize)]
pub struct SseSignalData {
    pub mint: String,
    pub timestamp: i64,
    pub quality: String,
    pub pool: String,
}

/// 假交易数据格式 (仅用于显示)
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize)]
pub struct FakeTradeData {
    pub signature: String,
    pub pool_state: String,
    pub signer: String,
    pub mint_address: String,
    pub total_base_sell: u64,
    pub virtual_base: u64,
    pub virtual_quote: u64,
    pub real_base_before: u64,
    pub real_quote_before: u64,
    pub real_base_after: u64,
    pub real_quote_after: u64,
    pub amount_in: u64,
    pub amount_out: u64,
    pub protocol_fee: u64,
    pub platform_fee: u64,
    pub share_fee: u64,
    pub trade_direction: String,
    pub pool_status: String,
    pub price_before: f64,
    pub price_after: f64,
    pub slippage: f64,
    pub actual_trade_price: f64,
    pub pool_base_vault: String,
    pub pool_quote_vault: String,
    #[serde(rename = "RECV_TS_US")]
    pub recv_ts_us: u64,
    #[serde(rename = "TYPE")]
    pub trade_type: String,
    #[serde(rename = "SIGNATURE")]
    pub tx_signature: String,
    #[serde(rename = "MINT")]
    pub token_mint: String,
}

/// SSE信号订阅服务
pub struct SseSignalSubscriber {
    high_quality_url: String,
    low_quality_url: String,
    redis_client: redis::Client,
    pump_channel: String,
    bonk_channel: String,
}

impl SseSignalSubscriber {
    pub fn new(
        high_quality_url: String,
        low_quality_url: String,
        redis_client: redis::Client,
        pump_channel: String,
        bonk_channel: String,
    ) -> Self {
        Self {
            high_quality_url,
            low_quality_url,
            redis_client,
            pump_channel,
            bonk_channel,
        }
    }

    /// 启动SSE订阅服务
    pub async fn start(&self) -> Result<()> {
        info!("启动SSE信号订阅服务...");
        
        // 启动高质量信号订阅
        let high_quality_url = self.high_quality_url.clone();
        let redis_client = self.redis_client.clone();
        let pump_channel = self.pump_channel.clone();
        let bonk_channel = self.bonk_channel.clone();
        tokio::spawn(async move {
            Self::subscribe_sse_stream(high_quality_url, "high", redis_client, pump_channel, bonk_channel).await;
        });

        // 启动低质量信号订阅
        let low_quality_url = self.low_quality_url.clone();
        let redis_client = self.redis_client.clone();
        let pump_channel = self.pump_channel.clone();
        let bonk_channel = self.bonk_channel.clone();
        tokio::spawn(async move {
            Self::subscribe_sse_stream(low_quality_url, "low", redis_client, pump_channel, bonk_channel).await;
        });

        info!("SSE信号订阅服务已启动");
        
        // 保持服务运行
        std::future::pending::<()>().await;
        Ok(())
    }

    /// 订阅单个SSE流
    async fn subscribe_sse_stream(
        url: String, 
        quality: &str,
        redis_client: redis::Client,
        pump_channel: String,
        bonk_channel: String,
    ) {
        info!("开始订阅{}质量SSE信号: {}", quality, url);
        
        loop {
            match Self::connect_and_subscribe(&url, &redis_client, &pump_channel, &bonk_channel).await {
                Ok(_) => {
                    debug!("{}质量SSE连接意外断开，正在重连...", quality);
                },
                Err(_) => {
                    // 静默重试，不输出错误日志
                }
            }
            tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
        }
    }

    /// 连接并订阅SSE流
    async fn connect_and_subscribe(
        url: &str,
        redis_client: &redis::Client,
        pump_channel: &str,
        bonk_channel: &str,
    ) -> Result<()> {
        let client = reqwest::Client::new();
        let response = client
            .get(url)
            .header("Accept", "text/event-stream")
            .header("Cache-Control", "no-cache")
            .send()
            .await
            .context("发送SSE请求失败")?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!("SSE请求失败，状态码: {}", response.status()));
        }

        let mut stream = response.bytes_stream().eventsource();

        while let Some(event) = stream.next().await {
            match event {
                Ok(event) => {
                    debug!("🔍 收到SSE事件: {:?}", event);
                    
                    if !event.data.is_empty() {
                        match serde_json::from_str::<SseSignalData>(&event.data) {
                            Ok(signal_data) => {
                                // 处理SSE信号数据
                                Self::process_and_send_signal_data(signal_data, redis_client, pump_channel, bonk_channel).await;
                            }
                            Err(e) => {
                                warn!("❌ 解析SSE信号数据失败: {}, 原始数据: {}", e, event.data);
                            }
                        }
                    }
                }
                Err(_) => {
                    break;
                }
            }
        }

        Ok(())
    }

    /// 处理信号数据，暂存等待真实交易数据
    async fn process_and_send_signal_data(
        signal_data: SseSignalData,
        _redis_client: &redis::Client,
        _pump_channel: &str,
        _bonk_channel: &str,
    ) {
        // 将信号存储到待处理列表中，等待相同mint的真实交易 - 无锁操作
        let mint = signal_data.mint.clone();
        PENDING_SSE_SIGNALS.insert(mint.clone(), signal_data);
    }

    /// 根据真实交易数据创建假交易文本，只替换签名者地址和签名，强制类型为Buy
    pub fn create_fake_trade_from_real(real_trade_text: &str) -> String {
        let mut result = String::new();
        let fake_signature = format!("FAKE{}", uuid::Uuid::new_v4().simple());
        
        for line in real_trade_text.lines() {
            if line.contains("签名者地址:") {
                // pump格式：签名者地址: 原地址 -> 签名者地址: 1111111111111111111111111111superai
                result.push_str("签名者地址: 1111111111111111111111111111superai\n");
            } else if line.trim().starts_with("signer\":") {
                // bonk格式：替换signer字段
                result.push_str("signer\": \"1111111111111111111111111111superai\",\n");
            } else if line.contains("SIGNATURE:") {
                // pump格式：替换签名
                result.push_str(&format!("SIGNATURE: {}\n", fake_signature));
            } else if line.trim().starts_with("signature\":") {
                // bonk格式：替换signature字段
                result.push_str(&format!("signature\": \"{}\",\n", fake_signature));
            } else if line.contains("TYPE:") {
                // pump格式：强制设置为Buy类型
                result.push_str("TYPE: Buy\n");
            } else if line.trim().starts_with("trade_direction\":") {
                // bonk格式：强制设置为Buy类型
                result.push_str("trade_direction\": \"Buy\",\n");
            } else {
                // 其他行保持不变
                result.push_str(line);
                result.push('\n');
            }
        }
        
        // 移除最后一个多余的换行符
        if result.ends_with('\n') {
            result.pop();
        }
        
        result
    }

    /// 获取或创建Redis连接
    async fn get_redis_connection(redis_client: &redis::Client) -> Result<redis::aio::MultiplexedConnection> {
        let mut pool = REDIS_CONNECTION_POOL.lock().await;

        if let Some(conn) = pool.take() {
            // 测试连接是否还有效
            if Self::test_connection(&conn).await {
                return Ok(conn);
            }
        }

        // 创建新连接
        let conn = redis_client.get_multiplexed_async_connection().await?;
        Ok(conn)
    }

    /// 测试Redis连接是否有效
    async fn test_connection(conn: &redis::aio::MultiplexedConnection) -> bool {
        // 简单测试：尝试ping
        match redis::cmd("PING").query_async::<_, String>(&mut conn.clone()).await {
            Ok(response) => response == "PONG",
            Err(_) => false,
        }
    }

    /// 归还Redis连接到池中
    async fn return_connection(conn: redis::aio::MultiplexedConnection) {
        let mut pool = REDIS_CONNECTION_POOL.lock().await;
        *pool = Some(conn);
    }

    /// 检查是否有待处理的信号并创建假交易
    pub async fn check_and_create_fake_trade(mint: &str, real_trade_text: &str, redis_client: &redis::Client, pump_channel: &str, bonk_channel: &str) -> bool {
        // 无锁操作：原子性地查找并移除信号
        if let Some((_, signal_data)) = PENDING_SSE_SIGNALS.remove(mint) {
            // 创建基于真实数据的假交易
            let fake_trade_text = Self::create_fake_trade_from_real(real_trade_text);

            // 选择正确的频道
            let channel = if signal_data.pool == "bonk" {
                bonk_channel
            } else {
                pump_channel
            };

            // 使用连接池发送假交易
            match Self::get_redis_connection(redis_client).await {
                Ok(mut conn) => {
                    if let Err(e) = conn.publish::<_, _, ()>(channel, &fake_trade_text).await {
                        error!("❌ 发送假交易到Redis失败: {}", e);
                    }

                    // 归还连接到池中
                    Self::return_connection(conn).await;
                }
                Err(e) => {
                    error!("❌ 获取Redis连接失败: {}", e);
                }
            }

            true
        } else {
            false
        }
    }

    /// 创建pump格式的交易文本（用于Redis）
    fn create_pump_trade_text(signal_data: &SseSignalData) -> String {
        format!(
            "TYPE: Buy\n\
            SIGNATURE: 0\n\
            签名者地址: 1111111111111111111111111111superai\n\
            MINT: {}\n\
            TOKEN AMOUNT: 0\n\
            SOL COST: 0.000000 SOL\n\
            当前价格: 0.0\n\
            创造者地址: 0\n\
            创作者金库地址: 0",
            signal_data.mint
        )
    }

    /// 创建bonk格式的交易文本（用于Redis）- JSON格式
    fn create_bonk_trade_text(signal_data: &SseSignalData) -> String {
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_micros() as u64;
            
        // bonk格式需要JSON
        format!(
            r#"{{
    "signature": "0",
    "pool_state": "0", 
    "signer": "1111111111111111111111111111superai",
    "mint_address": "{}",
    "total_base_sell": 0,
    "virtual_base": 0,
    "virtual_quote": 0,
    "real_base_before": 0,
    "real_quote_before": 0,
    "real_base_after": 0,
    "real_quote_after": 0,
    "amount_in": 0,
    "amount_out": 0,
    "protocol_fee": 0,
    "platform_fee": 0,
    "share_fee": 0,
    "trade_direction": "Buy",
    "pool_status": "Fund", 
    "price_before": 0.0,
    "price_after": 0.0,
    "slippage": 0.0,
    "actual_trade_price": 0.0,
    "pool_base_vault": "0",
    "pool_quote_vault": "0",
    "RECV_TS_US": {}
}}"#,
            signal_data.mint, current_time
        )
    }

    /// 根据SSE信号创建假交易数据
    fn create_fake_trade_data(signal_data: &SseSignalData) -> FakeTradeData {
        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_micros() as u64;

        // 生成随机的假交易数据
        let fake_signature = Self::generate_fake_signature();
        let fake_signer = Self::generate_fake_address();
        let fake_pool_state = Self::generate_fake_address();
        let fake_base_vault = Self::generate_fake_address();
        let fake_quote_vault = Self::generate_fake_address();

        // 模拟交易参数
        let virtual_base = 1066666666666666u64;
        let virtual_quote = 28333333333u64;
        let amount_in = 1053652872982u64;
        let amount_out = 141203086u64;
        
        // 计算价格
        let price_before = virtual_quote as f64 / virtual_base as f64;
        let real_base_before = 595284012450164u64;
        let real_quote_before = 35780655532u64;
        let real_base_after = real_base_before - amount_out;
        let real_quote_after = real_quote_before + amount_in;
        let price_after = real_quote_after as f64 / real_base_after as f64;
        let slippage = (price_after - price_before) / price_before * 100.0;
        let actual_trade_price = amount_in as f64 / amount_out as f64;

        FakeTradeData {
            signature: fake_signature.clone(),
            pool_state: fake_pool_state,
            signer: fake_signer,
            mint_address: signal_data.mint.clone(),
            total_base_sell: 800000000000000,
            virtual_base,
            virtual_quote,
            real_base_before,
            real_quote_before,
            real_base_after,
            real_quote_after,
            amount_in,
            amount_out,
            protocol_fee: 357477,
            platform_fee: 1429904,
            share_fee: 0,
            trade_direction: "Buy".to_string(),
            pool_status: "Fund".to_string(),
            price_before,
            price_after,
            slippage,
            actual_trade_price,
            pool_base_vault: fake_base_vault,
            pool_quote_vault: fake_quote_vault,
            recv_ts_us: current_time,
            trade_type: "Buy".to_string(),
            tx_signature: fake_signature,
            token_mint: signal_data.mint.clone(),
        }
    }

    /// 生成假的交易签名
    fn generate_fake_signature() -> String {
        let uuid1 = Uuid::new_v4().simple().to_string();
        let uuid2 = Uuid::new_v4().simple().to_string();
        format!("{}{}fake", &uuid1[..32], &uuid2[..32])
    }

    /// 生成假的Solana地址
    fn generate_fake_address() -> String {
        let uuid = Uuid::new_v4().simple().to_string();
        format!("{}fake{}", &uuid[..28], &uuid[28..])
    }
}