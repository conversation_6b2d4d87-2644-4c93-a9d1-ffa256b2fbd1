use base64::Engine;
use log::{debug, warn, info};
use std::sync::Arc;
use tokio::sync::OnceCell;

use crate::plugins::bonk::models::{CpiTradeEvent, LogTradeEvent, PoolStatus, TradeDirection};
use anyhow::Result;
use redis::AsyncCommands;
use solana_program::pubkey::Pubkey;
use crate::core::types::get_config;

// Vault识别相关结构体
#[derive(Debug, Clone)]
pub struct VaultInfo {
    pub base_vault: String,
    pub quote_vault: String,
}

// 支持的协议类型
#[derive(Debug, Clone, PartialEq)]
pub enum ProtocolType {
    RaydiumLaunchpad,
    RaydiumAmmV4,
    RaydiumClmm,
    RaydiumCpmm,
    OrcaWhirlpool,
    PumpFun,
    Unknown,
}

// Vault提取策略
#[derive(Debug, Clone)]
pub enum VaultExtractionStrategy {
    // 基于固定账户位置
    FixedPosition {
        base_vault_index: usize,
        quote_vault_index: usize,
    },
    // 基于账户数据解析（从pool状态中解析）
    FromPoolState {
        pool_account_index: usize,
        discriminator: [u8; 8],
        base_vault_offset: usize,
        quote_vault_offset: usize,
    },
    // 基于语义匹配（通过账户类型和特征匹配）
    SemanticMatch {
        vault_patterns: Vec<VaultPattern>,
    },
}

#[derive(Debug, Clone)]
pub struct VaultPattern {
    pub account_index: usize,
    pub is_base_vault: bool,
    pub expected_owner: Option<String>, // Token Program ID
}

// 协议识别配置
pub struct ProtocolConfig {
    pub protocol_type: ProtocolType,
    pub program_id: &'static str,
    pub extraction_strategy: VaultExtractionStrategy,
}

// 支持的协议配置
const SUPPORTED_PROTOCOLS: &[ProtocolConfig] = &[
    ProtocolConfig {
        protocol_type: ProtocolType::RaydiumLaunchpad,
        program_id: "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj",
        extraction_strategy: VaultExtractionStrategy::FixedPosition {
            base_vault_index: 8,
            quote_vault_index: 9,
        },
    },
    ProtocolConfig {
        protocol_type: ProtocolType::RaydiumAmmV4,
        program_id: "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8",
        extraction_strategy: VaultExtractionStrategy::FixedPosition {
            base_vault_index: 4,
            quote_vault_index: 5,
        },
    },
    ProtocolConfig {
        protocol_type: ProtocolType::RaydiumCpmm,
        program_id: "CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C",
        extraction_strategy: VaultExtractionStrategy::FromPoolState {
            pool_account_index: 4, // pool_state通常在索引4
            discriminator: [0xf7, 0xed, 0xe3, 0xf5, 0xd7, 0xc3, 0xde, 0x46],
            base_vault_offset: 8 + 264,  // 8字节discriminator + 264字节偏移
            quote_vault_offset: 8 + 296, // 8字节discriminator + 296字节偏移
        },
    },
];

/// 识别协议类型
pub fn identify_protocol(account_keys: &[Vec<u8>], instruction_program_id_index: usize) -> Option<&'static ProtocolConfig> {
    if instruction_program_id_index >= account_keys.len() {
        return None;
    }
    
    let program_id = bs58::encode(&account_keys[instruction_program_id_index]).into_string();
    
    for config in SUPPORTED_PROTOCOLS {
        if config.program_id == program_id {

            return Some(config);
        }
    }
    

    None
}

/// 通用vault地址提取
pub fn extract_vault_addresses(
    account_keys: &[Vec<u8>],
    protocol_config: &ProtocolConfig,
    pool_state_data: Option<&[u8]>
) -> Option<VaultInfo> {
    match &protocol_config.extraction_strategy {
        VaultExtractionStrategy::FixedPosition { base_vault_index, quote_vault_index } => {
            extract_vaults_by_position(account_keys, *base_vault_index, *quote_vault_index)
        }
        VaultExtractionStrategy::FromPoolState { 
            pool_account_index,
            discriminator,
            base_vault_offset,
            quote_vault_offset
        } => {
            extract_vaults_from_pool_data(
                account_keys,
                *pool_account_index,
                discriminator,
                *base_vault_offset,
                *quote_vault_offset,
                pool_state_data
            )
        }
        VaultExtractionStrategy::SemanticMatch { vault_patterns } => {
            extract_vaults_by_semantic_match(account_keys, vault_patterns)
        }
    }
}

/// 基于固定位置提取vault地址
fn extract_vaults_by_position(
    account_keys: &[Vec<u8>],
    base_vault_index: usize,
    quote_vault_index: usize,
) -> Option<VaultInfo> {
    let base_vault = if account_keys.len() > base_vault_index {
        bs58::encode(&account_keys[base_vault_index]).into_string()
    } else {

        return None;
    };
    
    let quote_vault = if account_keys.len() > quote_vault_index {
        bs58::encode(&account_keys[quote_vault_index]).into_string()
    } else {

        return None;
    };
    

    
    Some(VaultInfo {
        base_vault,
        quote_vault,
    })
}

/// 从pool状态数据中提取vault地址
fn extract_vaults_from_pool_data(
    _account_keys: &[Vec<u8>],
    _pool_account_index: usize,
    discriminator: &[u8; 8],
    base_vault_offset: usize,
    quote_vault_offset: usize,
    pool_state_data: Option<&[u8]>,
) -> Option<VaultInfo> {
    // 如果提供了pool_state_data，直接使用
    if let Some(data) = pool_state_data {
        return parse_vaults_from_raw_pool_data(data, discriminator, base_vault_offset, quote_vault_offset);
    }
    
    // 否则，尝试从账户索引获取（这里需要账户数据，暂时不实现）
    
    None
}

/// 从原始pool数据解析vault地址
fn parse_vaults_from_raw_pool_data(
    data: &[u8],
    discriminator: &[u8; 8],
    base_vault_offset: usize,
    quote_vault_offset: usize,
) -> Option<VaultInfo> {
    // 检查数据大小
    if data.len() < quote_vault_offset + 32 {
        
        return None;
    }
    
    // 检查discriminator
    if &data[0..8] != discriminator {
        
        return None;
    }
    
    // 提取base_vault
    let base_vault_bytes = &data[base_vault_offset..base_vault_offset + 32];
    let base_vault = bs58::encode(base_vault_bytes).into_string();
    
    // 提取quote_vault
    let quote_vault_bytes = &data[quote_vault_offset..quote_vault_offset + 32];
    let quote_vault = bs58::encode(quote_vault_bytes).into_string();
    
    
    
    Some(VaultInfo {
        base_vault,
        quote_vault,
    })
}

/// 基于语义匹配提取vault地址
fn extract_vaults_by_semantic_match(
    account_keys: &[Vec<u8>],
    vault_patterns: &[VaultPattern],
) -> Option<VaultInfo> {
    let mut base_vault = None;
    let mut quote_vault = None;
    
    for pattern in vault_patterns {
        if pattern.account_index >= account_keys.len() {
            continue;
        }
        
        let account_address = bs58::encode(&account_keys[pattern.account_index]).into_string();
        
        // 这里可以添加更多的验证逻辑，比如检查账户owner
        if pattern.is_base_vault {
            base_vault = Some(account_address);
        } else {
            quote_vault = Some(account_address);
        }
    }
    
    if let (Some(base), Some(quote)) = (base_vault, quote_vault) {

        Some(VaultInfo {
            base_vault: base,
            quote_vault: quote,
        })
    } else {

        None
    }
}

/// 获取Redis连接
async fn get_redis_connection() -> Result<redis::aio::Connection, redis::RedisError> {
    // 使用配置中的Redis URL
    let client = redis::Client::open(get_config().redis.url.clone())?;
    client.get_async_connection().await
}

// 获取Redis连接函数已实现，不再需要专用发布连接

/// 解析CPI日志中的交易事件
pub async fn parse_cpi_log(
    log: &str, 
    signature: &str, 
    signer: &str, 
    mint_address: &str,
    base_vault: &str,
    quote_vault: &str
) -> Option<LogTradeEvent> {
    // 跳过非程序日志
    if !log.starts_with("Program log:") {
        return None;
    }

    // 获取日志内容，去除"Program log:"前缀
    let log_content = log.trim_start_matches("Program log:").trim();
    
    // 检查是否是指令日志（如 "Instruction: BuyExactIn" 或 "Instruction: SellExactIn"）
    if log_content.contains("Instruction: BuyExactIn") || log_content.contains("Instruction: SellExactIn") {
        let trade_direction = if log_content.contains("BuyExactIn") {
            TradeDirection::Buy
        } else {
            TradeDirection::Sell
        };
        
        // 返回基本的LogTradeEvent对象，但不再使用硬编码的值
        return Some(LogTradeEvent {
            signature: signature.to_string(),
            pool_state: "unknown".to_string(),
            signer: signer.to_string(),
            mint_address: mint_address.to_string(),
            total_base_sell: 0,
            virtual_base: 0,
            virtual_quote: 0,
            real_base_before: 0,
            real_quote_before: 0,
            real_base_after: 0,
            real_quote_after: 0,
            amount_in: 0,
            amount_out: 0,
            protocol_fee: 0,
            platform_fee: 0,
            share_fee: 0,
            trade_direction,
            pool_status: PoolStatus::Fund,
            price_before: 0.0,
            price_after: 0.0,
            slippage: 0.0,
            actual_trade_price: 0.0,
            pool_base_vault: "".to_string(),
            pool_quote_vault: "".to_string(),
        });
    }
    
    // 尝试Base64解码
    let decoded_bytes = match base64::engine::general_purpose::STANDARD.decode(log_content) {
        Ok(bytes) => bytes,
        Err(_) => return None,
    };
    
    // 直接调用parse_cpi_trade_event来处理解码后的字节
    parse_cpi_trade_event(&decoded_bytes, signature, signer, mint_address, base_vault, quote_vault).await
}

/// 从CPI指令数据解析交易事件
pub async fn parse_cpi_trade_event(
    data: &[u8],
    signature: &str,
    signer: &str,
    mint_address: &str,
    base_vault: &str,
    quote_vault: &str,
) -> Option<LogTradeEvent> {
    let start_time = std::time::Instant::now();
    
    // 检查数据是否有前缀
    let mut actual_data = data;
    
    // 检查是否有前缀
    if data.len() > 16 && &data[0..16] != CpiTradeEvent::DISCRIMINATOR {
        // 尝试查找discriminator
        for i in 0..std::cmp::min(32, data.len() - 16) {
            if data.len() >= i + 16 && &data[i..i+16] == CpiTradeEvent::DISCRIMINATOR {
                actual_data = &data[i..];
                break;
            }
        }
    }
    
    // 检查discriminator
    if actual_data.len() >= 16 {
        let discriminator = &actual_data[0..16];
        
        if discriminator == CpiTradeEvent::DISCRIMINATOR {
            // 解析CPI事件
            if let Some(cpi_event) = CpiTradeEvent::from_bytes(actual_data) {
                let parse_complete_time = std::time::Instant::now();
                let parse_duration = parse_complete_time.duration_since(start_time);
                
                // 转换数据
                let pool_state_str = bs58::encode(&cpi_event.pool_state).into_string();
                let signature_string = signature.to_string();
                
                let trade_direction = match cpi_event.trade_direction {
                    0 => TradeDirection::Buy,
                    1 => TradeDirection::Sell,
                    _ => TradeDirection::Buy,
                };
                
                let pool_status = match cpi_event.pool_status {
                    0 => PoolStatus::Fund,
                    1 => PoolStatus::Migrate,
                    2 => PoolStatus::Trade,
                    _ => PoolStatus::Fund,
                };
                
                // 计算买入前价格、买入后价格和滑点
                // real_quote精度9, real_base精度6
                let price_before = LogTradeEvent::calculate_price_before(
                    cpi_event.real_base_before, 
                    cpi_event.real_quote_before
                );
                
                let price_after = LogTradeEvent::calculate_price_after(
                    cpi_event.real_base_after, 
                    cpi_event.real_quote_after
                );
                
                let slippage = LogTradeEvent::calculate_slippage(price_before, price_after);

                // 计算实际成交价格
                let actual_trade_price = LogTradeEvent::calculate_actual_trade_price(
                    cpi_event.amount_in,
                    cpi_event.amount_out,
                    &trade_direction
                );

                // 直接使用传入的vault地址
                let pool_base_vault = base_vault.to_string();
                let pool_quote_vault = quote_vault.to_string();

                // 创建交易事件对象
                let event = LogTradeEvent {
                    signature: signature_string.clone(),
                    pool_state: pool_state_str,
                    signer: signer.to_string(),
                    mint_address: mint_address.to_string(),
                    total_base_sell: cpi_event.total_base_sell,
                    virtual_base: cpi_event.virtual_base,
                    virtual_quote: cpi_event.virtual_quote,
                    real_base_before: cpi_event.real_base_before,
                    real_quote_before: cpi_event.real_quote_before,
                    real_base_after: cpi_event.real_base_after,
                    real_quote_after: cpi_event.real_quote_after,
                    amount_in: cpi_event.amount_in,
                    amount_out: cpi_event.amount_out,
                    protocol_fee: cpi_event.protocol_fee,
                    platform_fee: cpi_event.platform_fee,
                    share_fee: cpi_event.share_fee,
                    trade_direction,
                    pool_status,
                    price_before,
                    price_after,
                    slippage,
                    actual_trade_price,
                    pool_base_vault,
                    pool_quote_vault,
                };

                let total_duration = start_time.elapsed();
                

                
                return Some(event);
            }
        }
    }
    
    None
}

/// Redis缓存函数 - 使用连接管理器优化性能
async fn cache_to_redis(signature: &str, event: &LogTradeEvent) -> Result<(), redis::RedisError> {
    use redis::AsyncCommands;
    
    // 获取Redis连接
    let mut conn = get_redis_connection().await?;
    
    // 将事件序列化为JSON（预优化：可考虑使用更快的序列化格式）
    let json_value = serde_json::to_string(event).map_err(|e| {
        redis::RedisError::from((redis::ErrorKind::TypeError, "JSON serialization failed", e.to_string()))
    })?;
    
    // 使用签名作为key，JSON作为value存储，设置TTL避免内存泄露
    conn.set_ex::<_, _, ()>(signature, json_value, 3600).await?;
    
    Ok(())
}

/// Redis发布函数 - 即时发布交易事件到频道
pub async fn publish_trade_event(signature: &str, event: &LogTradeEvent) -> Result<(), redis::RedisError> {
    use redis::AsyncCommands;
    
    // 获取Redis连接用于发布
    let mut conn = get_redis_connection().await?;
    
    // 交易方向和池状态的字符串表示
    let trade_direction_str = match event.trade_direction {
        TradeDirection::Buy => "Buy",
        TradeDirection::Sell => "Sell",
    };
    
    let pool_status_str = match event.pool_status {
        PoolStatus::Fund => "Fund",
        PoolStatus::Migrate => "Migrate",
        PoolStatus::Trade => "Trade",
    };
    
    // 创建按照给定样例格式的文本
    // 注意：严格按照给定顺序和格式
    let text_format = format!(
        r#"signature": "{}",
pool_state": "{}",
signer": "{}",
mint_address": "{}",
total_base_sell": {},
virtual_base": {},
virtual_quote": {},
real_base_before": {},
real_quote_before": {},
real_base_after": {},
real_quote_after": {},
amount_in": {},
amount_out": {},
protocol_fee": {},
platform_fee": {},
share_fee": {},
trade_direction": "{}",
pool_status": "{}",
price_before": {},
price_after": {},
slippage": {},
actual_trade_price": {},
pool_base_vault": "{}",
pool_quote_vault": "{}""#,
        signature,
        event.pool_state,
        event.signer,
        event.mint_address,
        event.total_base_sell,
        event.virtual_base,
        event.virtual_quote,
        event.real_base_before,
        event.real_quote_before,
        event.real_base_after,
        event.real_quote_after,
        event.amount_in,
        event.amount_out,
        event.protocol_fee,
        event.platform_fee,
        event.share_fee,
        trade_direction_str,
        pool_status_str,
        event.price_before,
        event.price_after,
        event.slippage,
        event.actual_trade_price,
        event.pool_base_vault,
        event.pool_quote_vault
    );
    
    // 发布到多个频道以支持不同的订阅者
    let trade_channel = format!("trade_events:{}", event.mint_address);
    let general_channel = "trade_events:all";
    
    // 并行发布到多个频道
    let publish1: Result<i32, redis::RedisError> = conn.publish(&trade_channel, &text_format).await;
    let publish2: Result<i32, redis::RedisError> = conn.publish(general_channel, &text_format).await;
    
    // 检查发布结果
    match (publish1, publish2) {
        (Ok(_subs1), Ok(_subs2)) => {
            // 降低日志级别，不再频繁输出
    
        },
        (Err(e), _) | (_, Err(e)) => {
            warn!("发布失败 {}: {}", signature, e);
            return Err(e);
        }
    }
    
    Ok(())
}

/// Redis发布函数 - 即时发布交易事件到频道
pub async fn publish_bonk_event(event: &LogTradeEvent, recv_timestamp_us: u64) -> Result<(), redis::RedisError> {
    use redis::AsyncCommands;
    
    // 获取Redis连接用于发布
    let client = redis::Client::open(crate::core::types::get_config().redis.url.clone())?;
    let mut conn = client.get_multiplexed_async_connection().await?;
    
    // 交易方向和池状态的字符串表示
    let trade_direction_str = match event.trade_direction {
        TradeDirection::Buy => "Buy",
        TradeDirection::Sell => "Sell",
    };
    
    let pool_status_str = match event.pool_status {
        PoolStatus::Fund => "Fund",
        PoolStatus::Migrate => "Migrate",
        PoolStatus::Trade => "Trade",
    };
    
    // 创建按照给定样例格式的文本
    // 注意：严格按照给定顺序和格式
    let text_format = format!(
        r#"signature": "{}",
pool_state": "{}",
signer": "{}",
mint_address": "{}",
total_base_sell": {},
virtual_base": {},
virtual_quote": {},
real_base_before": {},
real_quote_before": {},
real_base_after": {},
real_quote_after": {},
amount_in": {},
amount_out": {},
protocol_fee": {},
platform_fee": {},
share_fee": {},
trade_direction": "{}",
pool_status": "{}",
price_before": {},
price_after": {},
slippage": {},
actual_trade_price": {},
pool_base_vault": "{}",
pool_quote_vault": "{}"\nRECV_TS_US: {}"#,
        event.signature,
        event.pool_state,
        event.signer,
        event.mint_address,
        event.total_base_sell,
        event.virtual_base,
        event.virtual_quote,
        event.real_base_before,
        event.real_quote_before,
        event.real_base_after,
        event.real_quote_after,
        event.amount_in,
        event.amount_out,
        event.protocol_fee,
        event.platform_fee,
        event.share_fee,
        trade_direction_str,
        pool_status_str,
        event.price_before,
        event.price_after,
        event.slippage,
        event.actual_trade_price,
        event.pool_base_vault,
        event.pool_quote_vault,
        recv_timestamp_us
    );
    
    // 发布到Redis - 使用bonk专用频道
    let channel = &crate::core::types::get_config().redis.bonk_channel;
    
    // 发布到Redis
    let publish_result: Result<i32, redis::RedisError> = conn.publish(channel, &text_format).await;
    
    match publish_result {
        Ok(_) => {
            info!("🚀 [BONK-DEBUG] 成功发布bonk交易到频道 {}: mint={}, price={}", 
                channel, event.mint_address, event.price_after);
        },
        Err(e) => {
            log::warn!("Bonk交易发布失败 {}: {}", event.signature, e);
            return Err(e);
        }
    }
    
    Ok(())
}

/// 解析交易日志列表，找出并解析所有CPI事件
pub async fn parse_transaction_logs(
    logs: &[String], 
    signature: &str, 
    signer: &str, 
    mint_address: &str,
    base_vault: &str,
    quote_vault: &str
) -> Vec<LogTradeEvent> {
    let mut events = Vec::new();
    
    for log in logs.iter() {
        if let Some(event) = parse_cpi_log(log, signature, signer, mint_address, base_vault, quote_vault).await {
            events.push(event);
        }
    }
    
    events
}