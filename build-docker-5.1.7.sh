#!/bin/bash

# =============================================================================
# 磐石钱包管理系统 v5.1.7 Docker镜像构建脚本
# =============================================================================

set -e

# 版本信息
VERSION="5.1.7"
IMAGE_NAME="panshi-wallet"
FULL_TAG="${IMAGE_NAME}:${VERSION}"
LATEST_TAG="${IMAGE_NAME}:latest"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}===============================================================================${NC}"
echo -e "${GREEN}🚀 磐石钱包管理系统 v${VERSION} Docker镜像构建${NC}"
echo -e "${BLUE}===============================================================================${NC}"
echo -e "${YELLOW}📦 镜像标签: ${FULL_TAG}${NC}"
echo -e "${YELLOW}🆕 新增功能:${NC}"
echo -e "   • AI信号智能识别与处理"
echo -e "   • 高级跟卖逻辑优化"
echo -e "   • 无锁架构性能提升"
echo -e "   • Redis连接池优化"
echo -e "   • 假交易日志清理"
echo -e "${YELLOW}📋 包含组件:${NC}"
echo -e "   • 交易后端 (newcopy_v2) - Solana 1.16"
echo -e "   • 监控后端 (copy-bot) - Solana 2.1"
echo -e "   • Redis数据库"
echo -e "   • 前端界面 (Nginx)"
echo -e "${BLUE}===============================================================================${NC}"

# 检查必要文件
echo -e "${YELLOW}🔍 检查构建文件...${NC}"

required_paths=(
    "Dockerfile.all-in-one"
    "qingqikaifaxxxxxxxxxxxxxxxxx/copybot-fix-price-debug-log/newcopy_v2"
    "taoli"
    "2.0/wallet-admin-frontend"
)

for path in "${required_paths[@]}"; do
    if [ ! -e "$path" ]; then
        echo -e "${RED}❌ 缺少必要文件/目录: $path${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ $path${NC}"
done

# 显示构建信息
echo -e "\n${YELLOW}📊 构建信息:${NC}"
echo -e "   • Docker版本: $(docker --version)"
echo -e "   • 构建时间: $(date)"
echo -e "   • 构建用户: $(whoami)"

# 开始构建
echo -e "\n${BLUE}🔨 开始构建Docker镜像...${NC}"
echo -e "${YELLOW}⏳ 这可能需要几分钟时间，请耐心等待...${NC}"

# 构建镜像
if docker build -f Dockerfile.all-in-one -t "$FULL_TAG" -t "$LATEST_TAG" .; then
    echo -e "\n${GREEN}✅ Docker镜像构建成功！${NC}"
else
    echo -e "\n${RED}❌ Docker镜像构建失败！${NC}"
    exit 1
fi

# 显示镜像信息
echo -e "\n${BLUE}===============================================================================${NC}"
echo -e "${GREEN}📦 构建完成的镜像信息:${NC}"
echo -e "${BLUE}===============================================================================${NC}"

docker images | grep "$IMAGE_NAME" | head -2

# 显示镜像大小
IMAGE_SIZE=$(docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep "$IMAGE_NAME" | grep "$VERSION" | awk '{print $3}')
echo -e "\n${YELLOW}📏 镜像大小: ${IMAGE_SIZE}${NC}"

# 运行建议
echo -e "\n${BLUE}===============================================================================${NC}"
echo -e "${GREEN}🚀 运行建议:${NC}"
echo -e "${BLUE}===============================================================================${NC}"
echo -e "${YELLOW}快速启动:${NC}"
echo -e "   docker run -d -p 80:80 --name panshi-wallet-v5.1.7 $FULL_TAG"
echo -e ""
echo -e "${YELLOW}生产环境启动:${NC}"
echo -e "   docker run -d \\"
echo -e "     -p 80:80 \\"
echo -e "     --name panshi-wallet-v5.1.7 \\"
echo -e "     --restart unless-stopped \\"
echo -e "     -v /data/panshi:/app/logs \\"
echo -e "     $FULL_TAG"
echo -e ""
echo -e "${YELLOW}访问地址:${NC}"
echo -e "   • 前端界面: http://localhost"
echo -e "   • API接口: http://localhost/api/"
echo -e "   • 健康检查: http://localhost/api/v1/health"
echo -e ""
echo -e "${YELLOW}查看日志:${NC}"
echo -e "   docker logs -f panshi-wallet-v5.1.7"
echo -e ""
echo -e "${YELLOW}进入容器:${NC}"
echo -e "   docker exec -it panshi-wallet-v5.1.7 bash"

echo -e "\n${BLUE}===============================================================================${NC}"
echo -e "${GREEN}🎉 磐石钱包管理系统 v${VERSION} 构建完成！${NC}"
echo -e "${BLUE}===============================================================================${NC}"
