pub mod models;
pub mod decoders;
pub mod types;
pub mod handlers;

use crate::core::parsers::{TransactionParser, RawTransaction};
use crate::plugins::bonk::decoders::raydium::{parse_cpi_trade_event, parse_transaction_logs, publish_bonk_event};
use crate::core::stats::plugin_stats;
use crate::core::unified_publisher::send_to_unified_publisher;
use crate::core::types::UnifiedEvent;
use crossbeam_channel::Sender;
use async_trait::async_trait;
use anyhow::Result;
use std::sync::Arc;
use log::{debug, info, warn};

// 重新导出核心类型
pub use models::*;
pub use decoders::*;
pub use types::*;
pub use handlers::*;

// 插件常量定义
pub const BONK_PLUGIN_NAME: &str = "bonk";

/// Bonk解析器 - 零延迟实现
pub struct BonkParser {
    name: String,
    raydium_program_id: String,
    unified_sender: Option<Sender<UnifiedEvent>>,
}

impl BonkParser {
    pub fn new() -> Self {
        Self {
            name: "bonk_parser".to_string(),
            raydium_program_id: "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj".to_string(),
            unified_sender: None,
        }
    }

    /// 设置统一发布器发送端
    pub fn set_unified_sender(&mut self, sender: Sender<UnifiedEvent>) {
        self.unified_sender = Some(sender);
    }
    
    /// 提取Raydium上下文信息
    fn extract_raydium_context(&self, account_keys: &[Vec<u8>], tx: &yellowstone_grpc_proto::geyser::SubscribeUpdateTransaction) -> RaydiumContext {
        let mut context = RaydiumContext::default();
        
        if let Some(tx_info) = tx.transaction.as_ref() {
            if let Some(transaction) = tx_info.transaction.as_ref() {
                if let Some(message) = transaction.message.as_ref() {
                    for instruction in &message.instructions {
                        let program_id_index = instruction.program_id_index as usize;
                        if program_id_index < account_keys.len() {
                            let program_id = bs58::encode(&account_keys[program_id_index]).into_string();
                            
                            if program_id == self.raydium_program_id {
                                // 提取账户信息 - 保持bonk原有逻辑
                                if !instruction.accounts.is_empty() && instruction.accounts[0] < account_keys.len() as u8 {
                                    context.signer = bs58::encode(&account_keys[instruction.accounts[0] as usize]).into_string();
                                }
                                if instruction.accounts.len() > 9 && instruction.accounts[9] < account_keys.len() as u8 {
                                    context.mint = bs58::encode(&account_keys[instruction.accounts[9] as usize]).into_string();
                                }
                                if instruction.accounts.len() > 7 && instruction.accounts[7] < account_keys.len() as u8 {
                                    context.base_vault = bs58::encode(&account_keys[instruction.accounts[7] as usize]).into_string();
                                }
                                if instruction.accounts.len() > 8 && instruction.accounts[8] < account_keys.len() as u8 {
                                    context.quote_vault = bs58::encode(&account_keys[instruction.accounts[8] as usize]).into_string();
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }
        
        context
    }
}

#[async_trait]
impl TransactionParser for BonkParser {
    fn name(&self) -> &str {
        &self.name
    }
    
    /// 零延迟检查 - 只检查程序ID
    fn can_handle(&self, account_keys: &[Vec<u8>]) -> bool {
        account_keys.iter().any(|key| {
            let program_id = bs58::encode(key).into_string();
            program_id == self.raydium_program_id
        })
    }
    
    /// 零延迟解析 - 完全复用bonk原有逻辑
    async fn parse(&self, transaction: Arc<RawTransaction>) -> Result<()> {
        let start_time = std::time::Instant::now();
        
        // 记录接收统计
        plugin_stats::increment_received(BONK_PLUGIN_NAME.to_string());
        
        // 提取上下文
        let context = self.extract_raydium_context(&transaction.account_keys, &transaction.transaction);
        
        // 检查上下文数据是否完整
        let mut missing_fields = Vec::new();
        if context.mint.is_empty() { missing_fields.push("mint"); }
        if context.base_vault.is_empty() { missing_fields.push("base_vault"); }
        if context.quote_vault.is_empty() { missing_fields.push("quote_vault"); }
        
        if !missing_fields.is_empty() {
        }
        
        let mut parse_success = false;
        
        // 处理内部指令 - 保持bonk原有逻辑
        if let Some(tx_info) = transaction.transaction.transaction.as_ref() {
            if let Some(meta) = tx_info.meta.as_ref() {
                for inner_group in &meta.inner_instructions {
                    for inner_instruction in &inner_group.instructions {
                        let inner_program_id_index = inner_instruction.program_id_index as usize;
                        if inner_program_id_index < transaction.account_keys.len() {
                            let inner_program_id = bs58::encode(&transaction.account_keys[inner_program_id_index]).into_string();
                            
                            if inner_program_id == self.raydium_program_id {
                                // 直接调用bonk原有解析函数，保持完全一致的逻辑和格式
                                match parse_cpi_trade_event(
                                    &inner_instruction.data,
                                    &transaction.signature,
                                    &context.signer,
                                    &context.mint,
                                    &context.base_vault,
                                    &context.quote_vault,
                                ).await {
                                    Some(event) => {
                                        let parse_elapsed = start_time.elapsed().as_nanos() as u64;

                                        
                                        // 记录解析统计和耗时
                                        plugin_stats::increment_parsed(BONK_PLUGIN_NAME.to_string());
                                        plugin_stats::add_parse_time(BONK_PLUGIN_NAME.to_string(), parse_elapsed);
                                        parse_success = true;
                                        
                                        // 使用统一发布器发布事件
                                        if let Some(ref sender) = self.unified_sender {
                                            let mut bonk_text = event.to_text_format();
                                            bonk_text.push_str("\nRECV_TS_US: ");
                                            bonk_text.push_str(&transaction.recv_timestamp_us.to_string());
                                            if let Err(e) = send_to_unified_publisher(
                                                sender,
                                                bonk_text,
                                                BONK_PLUGIN_NAME.to_string(),
                                            ) {

                                                plugin_stats::increment_publish_error(BONK_PLUGIN_NAME.to_string());
                                            } else {

                                                // 注意：实际发布统计由统一发布器处理
                                            }
                                        } else {
                                            // 回退到直接发布（兼容性）

                                            let publish_start = std::time::Instant::now();
                                            if let Err(e) = publish_bonk_event(&event, transaction.recv_timestamp_us).await {

                                                plugin_stats::increment_publish_error(BONK_PLUGIN_NAME.to_string());
                                            } else {
                                                let publish_elapsed = publish_start.elapsed().as_nanos() as u64;

                                                plugin_stats::increment_published(BONK_PLUGIN_NAME.to_string());
                                                plugin_stats::add_publish_time(BONK_PLUGIN_NAME.to_string(), publish_elapsed);
                                            }
                                        }
                                        
                                        return Ok(());
                                    },
                                    None => {

                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // 处理日志 - 保持bonk原有逻辑
        if let Some(logs) = transaction.transaction.transaction.as_ref()
            .and_then(|tx| tx.meta.as_ref())
            .map(|meta| &meta.log_messages)
        {
            let events = parse_transaction_logs(
                logs,
                &transaction.signature,
                &context.signer,
                &context.mint,
                &context.base_vault,
                &context.quote_vault,
            ).await;
            
            // 处理从日志中解析出的事件
            let has_events = !events.is_empty();
            for event in events {
                let parse_elapsed = start_time.elapsed().as_nanos() as u64;
                
                // 记录解析统计和耗时
                plugin_stats::increment_parsed(BONK_PLUGIN_NAME.to_string());
                plugin_stats::add_parse_time(BONK_PLUGIN_NAME.to_string(), parse_elapsed);
                parse_success = true;
                
                // 使用统一发布器发布事件
                if let Some(ref sender) = self.unified_sender {
                    let mut bonk_text = event.to_text_format();
                    bonk_text.push_str("\nRECV_TS_US: ");
                    bonk_text.push_str(&transaction.recv_timestamp_us.to_string());
                    if let Err(e) = send_to_unified_publisher(
                        sender,
                        bonk_text,
                        BONK_PLUGIN_NAME.to_string(),
                    ) {
                        
                        plugin_stats::increment_publish_error(BONK_PLUGIN_NAME.to_string());
                    } else {
                        // 注意：实际发布统计由统一发布器处理
                    }
                } else {
                    // 回退到直接发布（兼容性）
                    
                    let publish_start = std::time::Instant::now();
                    if let Err(e) = publish_bonk_event(&event, transaction.recv_timestamp_us).await {
                        
                        plugin_stats::increment_publish_error(BONK_PLUGIN_NAME.to_string());
                    } else {
                        let publish_elapsed = publish_start.elapsed().as_nanos() as u64;
                        plugin_stats::increment_published(BONK_PLUGIN_NAME.to_string());
                        plugin_stats::add_publish_time(BONK_PLUGIN_NAME.to_string(), publish_elapsed);
                    }
                }
            }
            
            if !has_events && !parse_success {

            }
        }
        
        if !parse_success {

            plugin_stats::increment_parse_error(BONK_PLUGIN_NAME.to_string());
        }
        
        Ok(())
    }
}

impl Default for BonkParser {
    fn default() -> Self {
        Self::new()
    }
}

#[derive(Default)]
struct RaydiumContext {
    signer: String,
    mint: String,
    base_vault: String,
    quote_vault: String,
}